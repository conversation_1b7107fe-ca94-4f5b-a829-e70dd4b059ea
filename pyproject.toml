[project]
name = "windows-mcp"
version = "0.1.0"
description = "Lightweight MCP Server for interacting with Windows Operating System."
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "jeogeoa<PERSON><EMAIL>" }
]
readme = "README.md"
license = { file = "LICENSE" }
urls = { homepage = "https://github.com/CursorTouch" }
keywords = ["windows", "mcp", "ai", "desktop","ai agent"]
requires-python = ">=3.13"
dependencies = [
    "fastmcp>=2.8.1",
    "fuzzywuzzy>=0.18.0",
    "humancursor>=1.1.5",
    "ipykernel>=6.30.0",
    "live-inspect>=0.1.1",
    "markdownify>=1.1.0",
    "pillow>=11.2.1",
    "psutil>=7.0.0",
    "pyautogui>=0.9.54",
    "pygetwindow>=0.0.9",
    "python-levenshtein>=0.27.1",
    "requests>=2.32.3",
    "uiautomation>=2.0.24",
]
